// Assessment API Service
// Handles submission and retrieval of assessment results

import { AssessmentResult, AssessmentScores } from '../types/assessment-results';
import { getMockResultById, generateMockResult } from '../data/mockAssessmentResults';
import { calculateAllScores, validateAnswers } from '../utils/assessment-calculations';

// Simulate API delay
const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Submit assessment answers and get analysis
 */
export async function submitAssessment(
  answers: Record<number, number | null>
): Promise<{ resultId: string; status: string }> {
  // Validate answers
  const validation = validateAnswers(answers);
  if (!validation.isValid) {
    throw new Error(`Missing ${validation.missingQuestions.length} answers. Please complete all questions.`);
  }

  // Simulate API call delay
  await delay(1000);

  // Calculate scores
  const scores = calculateAllScores(answers);

  // Generate result ID
  const resultId = 'result-' + Date.now().toString(36);

  // In a real app, this would send data to backend
  // For now, we'll store in localStorage for demo purposes
  const result = generateMockResult(resultId);
  result.assessment_data = scores;
  
  localStorage.setItem(`assessment-result-${resultId}`, JSON.stringify(result));

  return {
    resultId,
    status: 'processing'
  };
}

/**
 * Get assessment result by ID
 */
export async function getAssessmentResult(resultId: string): Promise<AssessmentResult> {
  // Simulate API call delay
  await delay(500);

  // Try to get from localStorage first (for submitted assessments)
  const storedResult = localStorage.getItem(`assessment-result-${resultId}`);
  if (storedResult) {
    return JSON.parse(storedResult);
  }

  // Fall back to mock data
  const mockResult = getMockResultById(resultId);
  if (mockResult) {
    return mockResult;
  }

  // If not found, throw error
  throw new Error(`Assessment result with ID ${resultId} not found`);
}

/**
 * Get all assessment results for a user
 */
export async function getUserAssessmentResults(userId?: string): Promise<AssessmentResult[]> {
  // Simulate API call delay
  await delay(800);

  const results: AssessmentResult[] = [];

  // Get results from localStorage
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    if (key && key.startsWith('assessment-result-')) {
      const result = JSON.parse(localStorage.getItem(key)!);
      if (!userId || result.userId === userId) {
        results.push(result);
      }
    }
  }

  // Add mock results for demo
  const mockResults = [
    getMockResultById('result-001'),
    getMockResultById('result-002')
  ].filter(Boolean) as AssessmentResult[];

  return [...results, ...mockResults].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );
}

/**
 * Check assessment processing status
 */
export async function checkAssessmentStatus(resultId: string): Promise<{
  status: 'queued' | 'processing' | 'completed' | 'failed';
  progress?: number;
}> {
  // Simulate API call delay
  await delay(300);

  try {
    const result = await getAssessmentResult(resultId);
    return {
      status: result.status,
      progress: result.status === 'completed' ? 100 : 
               result.status === 'processing' ? 75 : 
               result.status === 'queued' ? 25 : 0
    };
  } catch (error) {
    return { status: 'failed' };
  }
}

/**
 * Delete assessment result
 */
export async function deleteAssessmentResult(resultId: string): Promise<void> {
  // Simulate API call delay
  await delay(500);

  // Remove from localStorage
  localStorage.removeItem(`assessment-result-${resultId}`);
}

/**
 * Generate AI analysis for assessment scores
 * This would typically call an AI service, but for demo we'll use mock data
 */
export async function generateAIAnalysis(scores: AssessmentScores): Promise<{
  personaTitle: string;
  description: string;
  strengths: string[];
  recommendations: string[];
}> {
  // Simulate AI processing delay
  await delay(2000);

  // Simple rule-based analysis for demo
  const { riasec, ocean, viaIs } = scores;

  // Determine dominant traits
  const dominantRiasec = Object.entries(riasec).sort(([,a], [,b]) => b - a)[0][0];
  const highOpenness = ocean.openness > 70;
  const highCreativity = viaIs.creativity > 80;

  let personaTitle = 'The Balanced Individual';
  let description = 'Anda memiliki profil kepribadian yang seimbang dengan berbagai kekuatan yang dapat dikembangkan.';

  // Generate persona based on dominant traits
  if (dominantRiasec === 'investigative' && highOpenness) {
    personaTitle = 'The Innovative Researcher';
    description = 'Anda adalah seorang peneliti inovatif yang memiliki keingintahuan tinggi dan kemampuan analitis yang kuat.';
  } else if (dominantRiasec === 'social' && ocean.extraversion > 70) {
    personaTitle = 'The People Leader';
    description = 'Anda adalah seorang pemimpin yang berfokus pada pengembangan orang dan memiliki kemampuan sosial yang luar biasa.';
  } else if (dominantRiasec === 'artistic' && highCreativity) {
    personaTitle = 'The Creative Visionary';
    description = 'Anda adalah seorang visioner kreatif yang mampu melihat kemungkinan-kemungkinan baru dan mengekspresikannya dengan unik.';
  }

  const strengths = [
    'Kemampuan adaptasi yang baik',
    'Motivasi internal yang kuat',
    'Kemampuan belajar yang tinggi'
  ];

  const recommendations = [
    'Eksplorasi peluang yang sesuai dengan kekuatan utama Anda',
    'Kembangkan kemampuan yang masih perlu ditingkatkan',
    'Pertimbangkan karir yang menggabungkan berbagai minat Anda'
  ];

  return {
    personaTitle,
    description,
    strengths,
    recommendations
  };
}

/**
 * Export assessment result as PDF (placeholder)
 */
export async function exportResultAsPDF(resultId: string): Promise<Blob> {
  // Simulate PDF generation delay
  await delay(1500);

  // In a real app, this would generate an actual PDF
  // For demo, we'll return a simple text blob
  const result = await getAssessmentResult(resultId);
  const content = `
Assessment Result Report
========================

Persona: ${result.persona_profile.title}
Date: ${new Date(result.createdAt).toLocaleDateString('id-ID')}

Description:
${result.persona_profile.description}

Top Strengths:
${result.persona_profile.strengths.map(s => `• ${s}`).join('\n')}

Career Recommendations:
${result.persona_profile.careerRecommendation.map(c => `• ${c.careerName} (${c.matchPercentage}% match)`).join('\n')}

Role Models:
${result.persona_profile.roleModel.join(', ')}
  `;

  return new Blob([content], { type: 'text/plain' });
}
