// User Statistics Service
// Calculates real-time stats based on user's assessment data

import { StatCard } from '../types/dashboard';
import { AssessmentResult } from '../types/assessment-results';
import { getUserAssessmentResults } from './assessment-api';

export interface UserStats {
  totalAnalysis: number;
  completed: number;
  processing: number;
  tokenBalance: number;
  assessmentResults: AssessmentResult[];
}

/**
 * Initialize demo data for new users
 */
async function initializeDemoData(userId: string): Promise<void> {
  // Check if user already has data
  const existingResults = await getUserAssessmentResults(userId);
  if (existingResults.length > 0) return;

  // Add some demo assessment results for new users
  const demoResults = [
    {
      id: `demo-result-1-${userId}`,
      userId,
      createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
      status: 'completed' as const,
      assessment_data: {
        riasec: { realistic: 45, investigative: 85, artistic: 72, social: 38, enterprising: 65, conventional: 42 },
        ocean: { openness: 88, conscientiousness: 67, extraversion: 45, agreeableness: 72, neuroticism: 25 },
        viaIs: {}
      },
      persona_profile: {
        title: 'The Creative Investigator',
        description: 'Your first assessment result showing strong investigative and creative traits.',
        strengths: ['Analytical thinking', 'Creativity', 'Problem solving'],
        recommendations: ['Consider research roles', 'Explore creative industries'],
        careerRecommendation: [{
          careerName: 'Data Scientist',
          careerProspect: {
            jobAvailability: 'high' as const,
            salaryPotential: 'super high' as const,
            careerProgression: 'high' as const,
            industryGrowth: 'super high' as const,
            skillDevelopment: 'high' as const
          },
          matchPercentage: 92
        }],
        roleModel: ['Marie Curie', 'Steve Jobs']
      }
    },
    {
      id: `demo-result-2-${userId}`,
      userId,
      createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 days ago
      status: 'completed' as const,
      assessment_data: {
        riasec: { realistic: 32, investigative: 58, artistic: 45, social: 89, enterprising: 76, conventional: 38 },
        ocean: { openness: 72, conscientiousness: 84, extraversion: 91, agreeableness: 88, neuroticism: 18 },
        viaIs: {}
      },
      persona_profile: {
        title: 'The People Leader',
        description: 'Your second assessment showing strong social and leadership capabilities.',
        strengths: ['Leadership', 'Communication', 'Team building'],
        recommendations: ['Explore management roles', 'Develop coaching skills'],
        careerRecommendation: [{
          careerName: 'HR Director',
          careerProspect: {
            jobAvailability: 'high' as const,
            salaryPotential: 'high' as const,
            careerProgression: 'super high' as const,
            industryGrowth: 'high' as const,
            skillDevelopment: 'high' as const
          },
          matchPercentage: 95
        }],
        roleModel: ['Oprah Winfrey', 'Simon Sinek']
      }
    }
  ];

  // Store demo results
  for (const result of demoResults) {
    localStorage.setItem(`assessment-result-${result.id}`, JSON.stringify(result));
  }
}

/**
 * Calculate user statistics based on their assessment data
 */
export async function calculateUserStats(userId?: string): Promise<UserStats> {
  try {
    // Initialize demo data for new users
    if (userId) {
      await initializeDemoData(userId);
    }

    // Get user's assessment results
    const assessmentResults = await getUserAssessmentResults(userId);

    // Calculate stats
    const totalAnalysis = assessmentResults.length;
    const completed = assessmentResults.filter(result => result.status === 'completed').length;
    const processing = assessmentResults.filter(result =>
      result.status === 'processing' || result.status === 'queued'
    ).length;

    // Calculate token balance based on user activity
    // Base tokens: 10, +5 for each completed assessment, -2 for each processing
    const baseTokens = 10;
    const completedBonus = completed * 5;
    const processingCost = processing * 2;
    const tokenBalance = Math.max(0, baseTokens + completedBonus - processingCost);

    return {
      totalAnalysis,
      completed,
      processing,
      tokenBalance,
      assessmentResults
    };
  } catch (error) {
    console.error('Error calculating user stats:', error);

    // Return default stats if error occurs
    return {
      totalAnalysis: 0,
      completed: 0,
      processing: 0,
      tokenBalance: 10, // Default starting tokens
      assessmentResults: []
    };
  }
}

/**
 * Convert user stats to StatCard format for dashboard display
 */
export function formatStatsForDashboard(userStats: UserStats): StatCard[] {
  return [
    {
      id: "analysis",
      value: userStats.totalAnalysis,
      label: "Total Analysis",
      color: "#dbeafe",
      icon: "MagnifyingGlass.svg"
    },
    {
      id: "completed",
      value: userStats.completed,
      label: "Completed",
      color: "#dbfce7",
      icon: "Check.svg"
    },
    {
      id: "processing",
      value: userStats.processing,
      label: "Processing",
      color: "#dbeafe",
      icon: "Cpu.svg"
    },
    {
      id: "balance",
      value: userStats.tokenBalance,
      label: "Token Balance",
      color: "#f3e8ff",
      icon: "Command.svg"
    }
  ];
}

/**
 * Get user's assessment history for the assessment table
 */
export function formatAssessmentHistory(userStats: UserStats) {
  // Get assessment history from localStorage (newly completed assessments)
  const localHistory = JSON.parse(localStorage.getItem('assessment-history') || '[]');

  // Combine with existing assessment results
  const assessmentHistory = userStats.assessmentResults.map((result, index) => ({
    id: index + 1,
    nama: result.persona_profile.title,
    tipe: "Personality Assessment",
    tanggal: new Date(result.createdAt).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }),
    status: result.status,
    resultId: result.id
  }));

  // Add local history items
  const combinedHistory = [...localHistory, ...assessmentHistory];

  // Remove duplicates based on resultId and sort by date (newest first)
  const uniqueHistory = combinedHistory.filter((item, index, self) =>
    index === self.findIndex(t => t.resultId === item.resultId)
  ).sort((a, b) => {
    // Convert tanggal back to Date for sorting
    const dateA = new Date(a.tanggal.split(' ').reverse().join('-'));
    const dateB = new Date(b.tanggal.split(' ').reverse().join('-'));
    return dateB.getTime() - dateA.getTime();
  });

  return uniqueHistory;
}

/**
 * Calculate progress data based on user's latest assessment
 */
export function calculateUserProgress(userStats: UserStats) {
  // Get the most recent completed assessment
  const latestAssessment = userStats.assessmentResults
    .filter(result => result.status === 'completed')
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];

  if (!latestAssessment) {
    // Return default progress if no completed assessments
    return [
      { label: "Investigative", value: 0 },
      { label: "Arts", value: 0 },
      { label: "Practical", value: 0 },
      { label: "Social", value: 0 },
      { label: "Leadership", value: 0 },
      { label: "Analytical", value: 0 },
    ];
  }

  // Extract RIASEC scores from the latest assessment
  const riasec = latestAssessment.assessment_data.riasec;
  
  return [
    { label: "Investigative", value: riasec.investigative },
    { label: "Arts", value: riasec.artistic },
    { label: "Practical", value: riasec.realistic },
    { label: "Social", value: riasec.social },
    { label: "Leadership", value: riasec.enterprising },
    { label: "Analytical", value: riasec.conventional },
  ];
}

/**
 * Get user activity summary
 */
export function getUserActivitySummary(userStats: UserStats) {
  const totalAssessments = userStats.totalAnalysis;
  const completionRate = totalAssessments > 0 ? 
    Math.round((userStats.completed / totalAssessments) * 100) : 0;
  
  const lastAssessment = userStats.assessmentResults
    .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];
  
  const lastAssessmentDate = lastAssessment ? 
    new Date(lastAssessment.createdAt).toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }) : 'Never';

  return {
    totalAssessments,
    completionRate,
    lastAssessmentDate,
    tokensEarned: userStats.completed * 5,
    tokensSpent: userStats.processing * 2,
    currentBalance: userStats.tokenBalance
  };
}

/**
 * Simulate adding a new assessment (for demo purposes)
 */
export async function simulateNewAssessment(userId?: string): Promise<string> {
  // This would typically call the actual assessment API
  // For demo, we'll create a mock result and store it
  const resultId = 'result-' + Date.now().toString(36);
  
  // Store in localStorage for demo
  const mockResult = {
    id: resultId,
    userId: userId || 'current-user',
    createdAt: new Date().toISOString(),
    status: 'processing',
    assessment_data: {
      riasec: { realistic: 0, investigative: 0, artistic: 0, social: 0, enterprising: 0, conventional: 0 },
      ocean: { openness: 0, conscientiousness: 0, extraversion: 0, agreeableness: 0, neuroticism: 0 },
      viaIs: {} // Empty for processing status
    },
    persona_profile: {
      title: 'Assessment in Progress',
      description: 'Your assessment is being processed...',
      strengths: [],
      recommendations: [],
      careerRecommendation: [],
      roleModel: []
    }
  };
  
  localStorage.setItem(`assessment-result-${resultId}`, JSON.stringify(mockResult));
  
  return resultId;
}

/**
 * Update assessment status (for demo purposes)
 */
export async function updateAssessmentStatus(resultId: string, status: 'completed' | 'failed'): Promise<void> {
  const stored = localStorage.getItem(`assessment-result-${resultId}`);
  if (stored) {
    const result = JSON.parse(stored);
    result.status = status;
    
    if (status === 'completed') {
      // Add some mock data for completed assessment
      result.assessment_data = {
        riasec: {
          realistic: Math.floor(Math.random() * 100),
          investigative: Math.floor(Math.random() * 100),
          artistic: Math.floor(Math.random() * 100),
          social: Math.floor(Math.random() * 100),
          enterprising: Math.floor(Math.random() * 100),
          conventional: Math.floor(Math.random() * 100)
        },
        ocean: {
          openness: Math.floor(Math.random() * 100),
          conscientiousness: Math.floor(Math.random() * 100),
          extraversion: Math.floor(Math.random() * 100),
          agreeableness: Math.floor(Math.random() * 100),
          neuroticism: Math.floor(Math.random() * 100)
        },
        viaIs: {} // Simplified for demo
      };
      
      result.persona_profile = {
        title: 'The Dynamic Achiever',
        description: 'A well-rounded individual with strong potential for growth.',
        strengths: ['Adaptability', 'Problem Solving', 'Communication'],
        recommendations: ['Explore leadership roles', 'Develop technical skills'],
        careerRecommendation: [{
          careerName: 'Business Analyst',
          careerProspect: {
            jobAvailability: 'high',
            salaryPotential: 'high',
            careerProgression: 'high',
            industryGrowth: 'high',
            skillDevelopment: 'high'
          },
          matchPercentage: 85
        }],
        roleModel: ['Steve Jobs', 'Oprah Winfrey']
      };
    }
    
    localStorage.setItem(`assessment-result-${resultId}`, JSON.stringify(result));
  }
}
