'use client';

import { useRouter } from 'next/navigation';
import { useAssessment } from '../../contexts/AssessmentContext';
import { assessmentTypes } from '../../data/assessmentQuestions';

interface AssessmentHeaderProps {
  currentQuestion?: number;
  totalQuestions?: number;
  assessmentName?: string;
  phase?: string;
}

export default function AssessmentHeader({
  currentQuestion = 1,
  totalQuestions = 44,
  assessmentName = "Big Five Personality",
  phase = "Phase 1"
}: AssessmentHeaderProps) {
  const router = useRouter();
  const { debugFillAllAssessments, debugFillCurrentAssessment, getCurrentAssessment } = useAssessment();

  const handleBackToDashboard = () => {
    router.push('/');
  };

  const handleDebugFillCurrent = () => {
    const currentAssessment = getCurrentAssessment();
    debugFillCurrentAssessment();
    alert(`Debug: Semua ${currentAssessment.questions.length} soal ${currentAssessment.name} telah diisi otomatis!`);
  };

  const handleDebugFillAll = () => {
    const confirmed = confirm('Debug: <PERSON><PERSON><PERSON>h Anda yakin ingin mengisi SEMUA assessment (Big Five, RIASEC, VIA) dengan jawaban acak?');
    if (confirmed) {
      debugFillAllAssessments();
      alert('Debug: Semua assessment telah diisi otomatis dengan jawaban acak!');
    }
  };

  return (
    <div className="flex items-center justify-between px-8 py-6 bg-transparent">
      <div className="flex items-center gap-2">
        <button
          onClick={handleBackToDashboard}
          className="flex items-center gap-2 px-4 py-2 rounded-full border border-[#E5E7EB] bg-white text-[#64707D] text-[16px] font-medium shadow-sm hover:bg-[#f5f7fb] transition"
          type="button"
        >
          <img src="/icons/CaretLeft.svg" alt="Back" className="w-4 h-4" />
          Kembali ke Dashboard
        </button>
        <span className="font-semibold text-lg ml-4">{phase}: {assessmentName}</span>
      </div>
      <div className="flex items-center gap-6">
        {/* Debug Buttons - Only show in development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="flex gap-2">
            <button
              onClick={handleDebugFillCurrent}
              className="px-3 py-1 rounded-md bg-blue-500 hover:bg-blue-600 text-white text-xs font-semibold transition"
              title="Debug: Isi assessment saat ini"
            >
              🐛 Fill Current
            </button>
            <button
              onClick={handleDebugFillAll}
              className="px-3 py-1 rounded-md bg-orange-500 hover:bg-orange-600 text-white text-xs font-semibold transition"
              title="Debug: Isi semua assessment"
            >
              🐛 Fill All
            </button>
          </div>
        )}

        <button className="text-[#6475e9] text-sm font-semibold">Simpan & Keluar</button>
      </div>
    </div>
  );
}
